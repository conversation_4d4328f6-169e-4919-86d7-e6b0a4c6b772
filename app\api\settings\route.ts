import { NextApiRequest, NextApiResponse } from 'next';
import { getSiteSettings, saveSiteSettings } from '../../../data/settings';
import { readSettingsFromFile, writeSettingsToFile, checkFileWritePermissions } from '../../../lib/settings-file';
import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';

// التحقق من صحة JWT token
function verifyToken(token: string) {
  try {
    return jwt.verify(token, JWT_SECRET) as any;
  } catch (error) {
    return null;
  }
}

// استخراج token من الطلب
function extractToken(req: NextApiRequest): string | null {
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  const tokenFromCookie = req.cookies.authToken;
  if (tokenFromCookie) {
    return tokenFromCookie;
  }

  return null;
}

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    switch (req.method) {
      case 'GET':
        // السماح بقراءة الإعدادات بدون مصادقة للاستخدام العام
        console.log('📖 قراءة الإعدادات...');

        // محاولة قراءة من الملف أولاً، ثم localStorage كبديل
        let settings;
        try {
          settings = readSettingsFromFile();
          console.log('✅ تم تحميل الإعدادات من الملف');
        } catch (error) {
          console.log('⚠️ فشل تحميل الإعدادات من الملف، استخدام localStorage');
          settings = getSiteSettings();
        }

        res.status(200).json(settings);
        break;

      case 'PUT':
        // التحقق من المصادقة للتعديل
        const token = extractToken(req);
        if (!token) {
          return res.status(401).json({
            success: false,
            message: 'Authentication required',
            messageAr: 'المصادقة مطلوبة'
          });
        }

        const decoded = verifyToken(token);
        if (!decoded) {
          return res.status(401).json({
            success: false,
            message: 'Invalid token',
            messageAr: 'رمز المصادقة غير صحيح'
          });
        }

        const updates = req.body;
        console.log('🔄 تحديث إعدادات الموقع:', Object.keys(updates));

        // التحقق من صلاحيات الكتابة
        if (!checkFileWritePermissions()) {
          return res.status(500).json({
            success: false,
            message: 'File write permissions denied',
            messageAr: 'لا توجد صلاحيات كتابة الملف'
          });
        }

        // قراءة الإعدادات الحالية من الملف
        let currentSettings;
        try {
          currentSettings = readSettingsFromFile();
        } catch (error) {
          console.log('⚠️ فشل قراءة الملف، استخدام الإعدادات الافتراضية');
          currentSettings = getSiteSettings();
        }

        // دمج الإعدادات الجديدة مع الحالية
        const mergedSettings = { ...currentSettings, ...updates };

        // حفظ في الملف أولاً
        const fileSaved = writeSettingsToFile(mergedSettings);
        if (!fileSaved) {
          return res.status(500).json({
            success: false,
            message: 'Failed to save settings to file',
            messageAr: 'فشل في حفظ الإعدادات في الملف'
          });
        }

        // حفظ في localStorage أيضاً للتوافق
        saveSiteSettings(mergedSettings);

        // قراءة الإعدادات المحدثة للتأكد
        const updatedSettings = readSettingsFromFile();

        console.log('✅ تم تحديث إعدادات الموقع بنجاح في الملف و localStorage');

        res.status(200).json({
          success: true,
          message: 'Settings updated successfully',
          messageAr: 'تم تحديث الإعدادات بنجاح',
          ...updatedSettings
        });
        break;

      default:
        res.setHeader('Allow', ['GET', 'PUT']);
        res.status(405).json({
          success: false,
          message: `Method ${req.method} Not Allowed`,
          messageAr: `الطريقة ${req.method} غير مسموحة`
        });
    }
  } catch (error) {
    console.error('❌ خطأ في API الإعدادات:', error);
    res.status(500).json({
      success: false,
      message: 'Internal Server Error',
      messageAr: 'خطأ في الخادم الداخلي',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
}
