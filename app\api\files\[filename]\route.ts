import { NextApiRequest, NextApiResponse } from 'next';
import { readSecureFile, getSecureFileInfo } from '../../../../lib/secure-upload';
import path from 'path';

// Cache control headers
const CACHE_MAX_AGE = 60 * 60 * 24 * 7; // أسبوع واحد

// MIME types للملفات
const MIME_TYPES: { [key: string]: string } = {
  '.jpg': 'image/jpeg',
  '.jpeg': 'image/jpeg',
  '.png': 'image/png',
  '.gif': 'image/gif',
  '.webp': 'image/webp'
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // السماح فقط بطلبات GET
  if (req.method !== 'GET') {
    return res.status(405).json({ 
      success: false, 
      message: 'Method not allowed' 
    });
  }

  try {
    const { filename } = req.query;

    // التحقق من صحة اسم الملف
    if (!filename || typeof filename !== 'string') {
      return res.status(400).json({
        success: false,
        message: 'Invalid filename'
      });
    }

    // منع Path Traversal attacks
    if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
      return res.status(400).json({
        success: false,
        message: 'Invalid filename format'
      });
    }

    // التحقق من امتداد الملف
    const ext = path.extname(filename).toLowerCase();
    if (!MIME_TYPES[ext]) {
      return res.status(400).json({
        success: false,
        message: 'Unsupported file type'
      });
    }

    // قراءة الملف الآمن
    const fileBuffer = readSecureFile(filename);
    if (!fileBuffer) {
      return res.status(404).json({
        success: false,
        message: 'File not found'
      });
    }

    // الحصول على معلومات الملف
    const fileInfo = getSecureFileInfo(filename);
    if (!fileInfo) {
      return res.status(404).json({
        success: false,
        message: 'File info not found'
      });
    }

    // إعداد headers الاستجابة
    const mimeType = MIME_TYPES[ext];
    
    res.setHeader('Content-Type', mimeType);
    res.setHeader('Content-Length', fileBuffer.length);
    res.setHeader('Cache-Control', `public, max-age=${CACHE_MAX_AGE}`);
    res.setHeader('ETag', `"${filename}-${fileInfo.mtime.getTime()}"`);
    
    // إضافة headers أمان
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('Content-Security-Policy', "default-src 'none'; img-src 'self'; style-src 'unsafe-inline'");
    
    // التحقق من If-None-Match للـ caching
    const ifNoneMatch = req.headers['if-none-match'];
    const etag = `"${filename}-${fileInfo.mtime.getTime()}"`;
    
    if (ifNoneMatch === etag) {
      return res.status(304).end();
    }

    // إرسال الملف
    res.status(200).send(fileBuffer);

  } catch (error) {
    console.error('File serving error:', error);
    
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
}
